import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import { <PERSON>r<PERSON><PERSON>, BarChart3, TrendingUp, Eye } from 'lucide-react';
import type { DashboardMetrics } from '../../types/dashboard';

interface MetricsCardsProps {
  metrics: DashboardMetrics;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    label: string;
  };
  description?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon, trend, description }) => {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="h-4 w-4 text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value.toLocaleString()}</div>
        {trend && (
          <div className="flex items-center space-x-1 text-xs text-muted-foreground mt-1">
            <TrendingUp className="h-3 w-3" />
            <span className="text-green-600 font-medium">+{trend.value}%</span>
            <span>{trend.label}</span>
          </div>
        )}
        {description && (
          <p className="text-xs text-muted-foreground mt-1">
            {description}
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export const MetricsCards: React.FC<MetricsCardsProps> = ({ metrics }) => {
  const cards = [
    {
      title: 'Total Active QR Codes',
      value: metrics.totalActiveQRCodes,
      icon: <QrCode className="h-4 w-4" />,
      description: 'Currently active QR codes'
    },
    {
      title: 'Total Scan Count',
      value: metrics.totalScanCount,
      icon: <BarChart3 className="h-4 w-4" />,
      trend: metrics.monthlyGrowth ? {
        value: metrics.monthlyGrowth,
        label: 'from last month'
      } : undefined,
      description: 'All-time total scans'
    },
    {
      title: "Today's Scans",
      value: metrics.todayScanCount,
      icon: <Eye className="h-4 w-4" />,
      trend: metrics.weeklyGrowth ? {
        value: metrics.weeklyGrowth,
        label: 'from last week'
      } : undefined,
      description: 'Scans recorded today'
    }
  ];

  return (
    <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
      {cards.map((card, index) => (
        <MetricCard
          key={index}
          title={card.title}
          value={card.value}
          icon={card.icon}
          trend={card.trend}
          description={card.description}
        />
      ))}
    </div>
  );
};

export default MetricsCards;
