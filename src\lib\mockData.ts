import type { QRCode, DashboardMetrics, QRAnalytics, ScanByDate, ScanByLocation, ScanByDevice, ScanByReferrer, RecentScan } from '../types/dashboard';

// Mock QR Codes data
export const mockQRCodes: QRCode[] = [
  {
    id: 'qr-001',
    name: 'Product Launch Campaign',
    url: 'https://example.com/product-launch',
    createdAt: new Date('2024-01-15'),
    scanCount: 1247,
    status: 'active',
    lastScanned: new Date('2024-01-20T14:30:00'),
    description: 'QR code for new product launch marketing campaign',
    tags: ['marketing', 'product', 'campaign']
  },
  {
    id: 'qr-002',
    name: 'Restaurant Menu',
    url: 'https://restaurant.com/menu',
    createdAt: new Date('2024-01-10'),
    scanCount: 892,
    status: 'active',
    lastScanned: new Date('2024-01-20T12:15:00'),
    description: 'Digital menu for restaurant customers',
    tags: ['restaurant', 'menu', 'food']
  },
  {
    id: 'qr-003',
    name: 'Event Registration',
    url: 'https://events.com/register/tech-conference',
    createdAt: new Date('2024-01-08'),
    scanCount: 456,
    status: 'active',
    lastScanned: new Date('2024-01-19T16:45:00'),
    description: 'Registration link for tech conference',
    tags: ['event', 'conference', 'registration']
  },
  {
    id: 'qr-004',
    name: 'WiFi Access',
    url: 'WIFI:T:WPA;S:OfficeNetwork;P:password123;;',
    createdAt: new Date('2024-01-05'),
    scanCount: 234,
    status: 'inactive',
    lastScanned: new Date('2024-01-18T09:20:00'),
    description: 'Office WiFi access credentials',
    tags: ['wifi', 'office', 'access']
  },
  {
    id: 'qr-005',
    name: 'Social Media Profile',
    url: 'https://instagram.com/company',
    createdAt: new Date('2024-01-03'),
    scanCount: 678,
    status: 'active',
    lastScanned: new Date('2024-01-20T11:30:00'),
    description: 'Company Instagram profile link',
    tags: ['social', 'instagram', 'profile']
  },
  {
    id: 'qr-006',
    name: 'Contact Information',
    url: 'BEGIN:VCARD\nVERSION:3.0\nFN:John Doe\nORG:Company Inc\nTEL:+1234567890\nEMAIL:<EMAIL>\nEND:VCARD',
    createdAt: new Date('2024-01-01'),
    scanCount: 123,
    status: 'active',
    lastScanned: new Date('2024-01-19T15:10:00'),
    description: 'Business contact vCard',
    tags: ['contact', 'vcard', 'business']
  }
];

// Mock Dashboard Metrics
export const mockDashboardMetrics: DashboardMetrics = {
  totalActiveQRCodes: 5,
  totalScanCount: 3630,
  todayScanCount: 127,
  weeklyGrowth: 12.5,
  monthlyGrowth: 28.3
};

// Generate mock scan data for the last 30 days
const generateScansByDate = (): ScanByDate[] => {
  const data: ScanByDate[] = [];
  const today = new Date();
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    const scans = Math.floor(Math.random() * 50) + 10; // Random scans between 10-60
    
    data.push({
      date: date.toISOString().split('T')[0],
      scans
    });
  }
  
  return data;
};

// Mock analytics data for individual QR codes
export const mockQRAnalytics: Record<string, QRAnalytics> = {
  'qr-001': {
    qrCodeId: 'qr-001',
    totalScans: 1247,
    uniqueScans: 892,
    scansByDate: generateScansByDate(),
    scansByLocation: [
      { country: 'United States', city: 'New York', scans: 456, percentage: 36.6 },
      { country: 'United Kingdom', city: 'London', scans: 234, percentage: 18.8 },
      { country: 'Canada', city: 'Toronto', scans: 187, percentage: 15.0 },
      { country: 'Germany', city: 'Berlin', scans: 156, percentage: 12.5 },
      { country: 'France', city: 'Paris', scans: 214, percentage: 17.1 }
    ],
    scansByDevice: [
      { device: 'Mobile', scans: 892, percentage: 71.5 },
      { device: 'Desktop', scans: 234, percentage: 18.8 },
      { device: 'Tablet', scans: 121, percentage: 9.7 }
    ],
    scansByReferrer: [
      { referrer: 'Direct', scans: 567, percentage: 45.5 },
      { referrer: 'Social Media', scans: 345, percentage: 27.7 },
      { referrer: 'Email', scans: 234, percentage: 18.8 },
      { referrer: 'Other', scans: 101, percentage: 8.1 }
    ],
    recentScans: [
      {
        id: 'scan-001',
        timestamp: new Date('2024-01-20T14:30:00'),
        location: 'New York, US',
        device: 'iPhone 15',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)',
        ipAddress: '***********'
      },
      {
        id: 'scan-002',
        timestamp: new Date('2024-01-20T13:15:00'),
        location: 'London, UK',
        device: 'Samsung Galaxy S24',
        userAgent: 'Mozilla/5.0 (Linux; Android 14)',
        ipAddress: '***********'
      }
    ]
  }
};

// Helper function to get paginated QR codes
export const getPaginatedQRCodes = (page: number = 1, itemsPerPage: number = 10) => {
  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedData = mockQRCodes.slice(startIndex, endIndex);
  
  return {
    data: paginatedData,
    pagination: {
      currentPage: page,
      totalPages: Math.ceil(mockQRCodes.length / itemsPerPage),
      totalItems: mockQRCodes.length,
      itemsPerPage
    }
  };
};
