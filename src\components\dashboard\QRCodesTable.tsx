import React from 'react';
import { format } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Eye, Download, MoreHorizontal } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import type { QRCode } from '../../types/dashboard';

interface QRCodesTableProps {
  qrCodes: QRCode[];
  onViewDetails: (qrCodeId: string) => void;
  onDownloadQR: (qrCodeId: string) => void;
}

const StatusBadge: React.FC<{ status: 'active' | 'inactive' }> = ({ status }) => {
  return (
    <Badge 
      variant={status === 'active' ? 'default' : 'secondary'}
      className={status === 'active' ? 'bg-green-100 text-green-800 hover:bg-green-200' : ''}
    >
      {status === 'active' ? 'Active' : 'Inactive'}
    </Badge>
  );
};

const ActionButtons: React.FC<{
  qrCodeId: string;
  onViewDetails: (id: string) => void;
  onDownloadQR: (id: string) => void;
}> = ({ qrCodeId, onViewDetails, onDownloadQR }) => {
  return (
    <div className="flex items-center space-x-1 sm:space-x-2">
      {/* Mobile: Show only icons */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => onViewDetails(qrCodeId)}
        className="h-8 px-2 sm:px-3"
      >
        <Eye className="h-3 w-3 sm:mr-1" />
        <span className="hidden sm:inline">View Details</span>
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={() => onDownloadQR(qrCodeId)}
        className="h-8 px-2 sm:px-3"
      >
        <Download className="h-3 w-3 sm:mr-1" />
        <span className="hidden sm:inline">Download</span>
      </Button>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreHorizontal className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => onViewDetails(qrCodeId)}>
            View Analytics
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onDownloadQR(qrCodeId)}>
            Download QR Code
          </DropdownMenuItem>
          <DropdownMenuItem>Edit QR Code</DropdownMenuItem>
          <DropdownMenuItem className="text-red-600">
            Delete QR Code
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

// Mobile card component for better mobile experience
const MobileQRCard: React.FC<{
  qrCode: QRCode;
  onViewDetails: (id: string) => void;
  onDownloadQR: (id: string) => void;
}> = ({ qrCode, onViewDetails, onDownloadQR }) => (
  <Card className="p-4">
    <div className="flex justify-between items-start mb-3">
      <div className="flex-1">
        <h3 className="font-medium text-sm">{qrCode.name}</h3>
        {qrCode.description && (
          <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
            {qrCode.description}
          </p>
        )}
      </div>
      <StatusBadge status={qrCode.status} />
    </div>

    <div className="grid grid-cols-2 gap-4 text-xs mb-3">
      <div>
        <span className="text-muted-foreground">Created:</span>
        <div className="font-medium">{format(qrCode.createdAt, 'MMM dd, yyyy')}</div>
      </div>
      <div>
        <span className="text-muted-foreground">Scans:</span>
        <div className="font-medium">{qrCode.scanCount.toLocaleString()}</div>
      </div>
    </div>

    {qrCode.tags && qrCode.tags.length > 0 && (
      <div className="flex flex-wrap gap-1 mb-3">
        {qrCode.tags.slice(0, 3).map((tag) => (
          <Badge key={tag} variant="outline" className="text-xs">
            {tag}
          </Badge>
        ))}
        {qrCode.tags.length > 3 && (
          <Badge variant="outline" className="text-xs">
            +{qrCode.tags.length - 3}
          </Badge>
        )}
      </div>
    )}

    <ActionButtons
      qrCodeId={qrCode.id}
      onViewDetails={onViewDetails}
      onDownloadQR={onDownloadQR}
    />
  </Card>
);

export const QRCodesTable: React.FC<QRCodesTableProps> = ({
  qrCodes,
  onViewDetails,
  onDownloadQR,
}) => {
  if (qrCodes.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>QR Codes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">No QR codes found.</p>
            <Button className="mt-4">Create Your First QR Code</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>QR Codes</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Desktop Table View */}
        <div className="hidden md:block rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Scan Count</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {qrCodes.map((qrCode) => (
                <TableRow key={qrCode.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{qrCode.name}</div>
                      {qrCode.description && (
                        <div className="text-sm text-muted-foreground truncate max-w-xs">
                          {qrCode.description}
                        </div>
                      )}
                      {qrCode.tags && qrCode.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-1">
                          {qrCode.tags.slice(0, 2).map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {qrCode.tags.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{qrCode.tags.length - 2}
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {format(qrCode.createdAt, 'MMM dd, yyyy')}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {format(qrCode.createdAt, 'HH:mm')}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="font-medium">{qrCode.scanCount.toLocaleString()}</div>
                    {qrCode.lastScanned && (
                      <div className="text-xs text-muted-foreground">
                        Last: {format(qrCode.lastScanned, 'MMM dd')}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <StatusBadge status={qrCode.status} />
                  </TableCell>
                  <TableCell className="text-right">
                    <ActionButtons
                      qrCodeId={qrCode.id}
                      onViewDetails={onViewDetails}
                      onDownloadQR={onDownloadQR}
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Mobile Card View */}
        <div className="md:hidden space-y-4">
          {qrCodes.map((qrCode) => (
            <MobileQRCard
              key={qrCode.id}
              qrCode={qrCode}
              onViewDetails={onViewDetails}
              onDownloadQR={onDownloadQR}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default QRCodesTable;
