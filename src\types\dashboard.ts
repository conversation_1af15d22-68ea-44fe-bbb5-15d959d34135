export interface QRCode {
  id: string;
  name: string;
  url: string;
  createdAt: Date;
  scanCount: number;
  status: 'active' | 'inactive';
  lastScanned?: Date;
  description?: string;
  tags?: string[];
}

export interface DashboardMetrics {
  totalActiveQRCodes: number;
  totalScanCount: number;
  todayScanCount: number;
  weeklyGrowth?: number;
  monthlyGrowth?: number;
}

export interface QRAnalytics {
  qrCodeId: string;
  totalScans: number;
  uniqueScans: number;
  scansByDate: ScanByDate[];
  scansByLocation: ScanByLocation[];
  scansByDevice: ScanByDevice[];
  scansByReferrer: ScanByReferrer[];
  recentScans: RecentScan[];
}

export interface ScanByDate {
  date: string;
  scans: number;
}

export interface ScanByLocation {
  country: string;
  city?: string;
  scans: number;
  percentage: number;
}

export interface ScanByDevice {
  device: string;
  scans: number;
  percentage: number;
}

export interface ScanByReferrer {
  referrer: string;
  scans: number;
  percentage: number;
}

export interface RecentScan {
  id: string;
  timestamp: Date;
  location: string;
  device: string;
  userAgent?: string;
  ipAddress?: string;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

export interface QRCodesTableProps {
  qrCodes: QRCode[];
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  onViewDetails: (qrCodeId: string) => void;
  onDownloadQR: (qrCodeId: string) => void;
}
