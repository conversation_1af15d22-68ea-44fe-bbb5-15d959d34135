import React, { useState, useEffect } from 'react';
import { MetricsCards } from './MetricsCards';
import { QRCodesTable } from './QRCodesTable';
import { TablePagination } from './TablePagination';
import { mockDashboardMetrics, getPaginatedQRCodes } from '../../lib/mockData';
import type { QRCode, PaginationInfo } from '../../types/dashboard';

export const DashboardContent: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [qrCodes, setQrCodes] = useState<QRCode[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10
  });

  // Load paginated data
  useEffect(() => {
    const result = getPaginatedQRCodes(currentPage, pageSize);
    setQrCodes(result.data);
    setPagination(result.pagination);
  }, [currentPage, pageSize]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const handleViewDetails = (qrCodeId: string) => {
    // Navigate to QR analytics page
    window.location.href = `/dashboard/qr-analytics/${qrCodeId}`;
  };

  const handleDownloadQR = (qrCodeId: string) => {
    // Handle QR code download
    console.log('Downloading QR code:', qrCodeId);
    // In a real app, this would trigger a download or open a modal
    alert(`Downloading QR code: ${qrCodeId}`);
  };

  return (
    <div className="space-y-6">
      {/* Dashboard Metrics */}
      <div>
        <h2 className="text-2xl font-bold tracking-tight mb-4">Dashboard Overview</h2>
        <MetricsCards metrics={mockDashboardMetrics} />
      </div>

      {/* QR Codes Section */}
      <div>
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4">
          <h2 className="text-xl font-semibold">QR Codes</h2>
          <button className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors text-sm sm:text-base">
            Create New QR Code
          </button>
        </div>
        
        <QRCodesTable
          qrCodes={qrCodes}
          onViewDetails={handleViewDetails}
          onDownloadQR={handleDownloadQR}
        />
        
        <TablePagination
          pagination={pagination}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      </div>
    </div>
  );
};

export default DashboardContent;
