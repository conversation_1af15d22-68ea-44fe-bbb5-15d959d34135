---
import DashboardLayout from '../../../layouts/DashboardLayout.astro';
import QRAnalyticsDetailWrapper from '../../../components/dashboard/QRAnalyticsDetailWrapper';

export async function getStaticPaths() {
  // In a real app, this would fetch from your database
  const qrCodeIds = ['qr-001', 'qr-002', 'qr-003', 'qr-004', 'qr-005', 'qr-006'];
  
  return qrCodeIds.map((qrid) => ({
    params: { qrid },
  }));
}

const { qrid } = Astro.params;
---

<DashboardLayout title={`QR Analytics - ${qrid} - QRAnalytica`}>
  <QRAnalyticsDetailWrapper qrCodeId={qrid} client:load />
</DashboardLayout>
